{"env": {"es2021": true, "node": true}, "extends": ["prettier", "standard-with-typescript"], "plugins": ["prettier", "@typescript-eslint"], "overrides": [], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "project": "./tsconfig.json"}, "rules": {"no-console": "off", "prettier/prettier": "error", "@typescript-eslint/dot-notation": "off", "@typescript-eslint/strict-boolean-expressions": "off", "@typescript-eslint/space-before-function-paren": "off", "@typescript-eslint/restrict-template-expressions": "off", "@typescript-eslint/triple-slash-reference": "off", "@typescript-eslint/indent": "off", "@typescript-eslint/no-misused-promises": "off", "@typescript-eslint/no-namespace": "off"}}