import 'dotenv/config'
import './config'

import express, { type Application, type Request, type Response } from 'express'
import { appMiddlewares } from './middleware/appMiddlewares'
import registerRoutes from './utils/registerRoutes'
import { initializeDatabase } from './config/database'
import errorMiddleware from './middleware/errorMiddleware'

interface IApp {
  app: Application
  port: number | string | undefined
}

async function createApp(): Promise<IApp> {
  // Connect to the database first
  await initializeDatabase()

  const port = process.env.PORT
  const app: Application = express()

  app.use(express.urlencoded({ limit: '50mb', extended: true }))
  app.use(express.json({ limit: '50mb' }))

  appMiddlewares(app)
  registerRoutes(app)

  app.use('*', (req: Request, res: Response) => {
    res.notFound()
  })

  /**
   * This is placed at the last middleware declaration
   */
  app.use(errorMiddleware)

  return { app, port }
}

try {
  void createApp().then(({ app, port }) => {
    app.listen(port, () => {
      console.log(`Server running in http://localhost:${port}`)
    })
  })
} catch (error: any) {
  console.error('WE OUTPUT THE ERROR HERE', error.message)
  process.exit(1)
}
