import { type Request, type Response, type NextFunction } from 'express'
import { type Document, type Model } from 'mongoose'

export interface IPagination {
  prev?: object
  next?: object
  limit?: number
}

export default <T extends Document>(IModel: Model<T>, populate?: string) =>
  async (req: Request, res: Response, next: NextFunction) => {
    let query

    const excludeKeys = ['select', 'sort', 'page', 'limit']
    const filteredQuery: Record<string, any> = { ...req.query }

    const queryString = JSON.stringify(
      Object.keys(filteredQuery).reduce((acc: Record<string, any>, key) => {
        if (!excludeKeys.includes(key)) {
          acc[key] = filteredQuery[key]
        }
        return acc
      }, {})
    )

    query = IModel.find(JSON.parse(queryString))

    query = query.select('-password -__v')

    // Selecting specific fields
    if (req.query.select) {
      if (typeof req.query.select === 'string') {
        const selectString = req.query.select.split(',').join(' ')
        query = query.select(selectString)
      }
    }

    // Sort the results
    if (req.query.sort) {
      if (typeof req.query.sort === 'string') {
        const sortString = req.query.sort.split(',').join(' ')
        query = query.sort(sortString)
      }
    } else {
      // Default sort results
      query = query.sort('-cratedAt')
    }

    // Pagination area
    const page: number = parseInt(req.query.page as string) || 1
    const limit: number = parseInt(req.query.limit as string) || 10
    const start: number = (page - 1) * limit
    const end: number = start + limit
    const count: number = await IModel.countDocuments()

    query = query.skip(start).limit(limit)

    let data
    if (populate && (populate !== undefined || populate !== null)) {
      data = query.populate(populate)
    }

    data = await query

    const pagination: IPagination = {}

    if (start > 0) {
      pagination.prev = {
        page: page - 1,
        limit
      }
    }

    if (end < count) {
      pagination.next = {
        page: page + 1,
        limit
      }
    }

    res.data = {
      pagination,
      itemCount: data.length,
      total: count,
      items: data
    }

    next()
  }
