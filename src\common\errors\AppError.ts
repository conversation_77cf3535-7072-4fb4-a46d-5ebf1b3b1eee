interface ResponseObject {
  success?: boolean
  name?: string
  message: any
  data?: any
  statusCode: number
}

interface AppError extends <PERSON>rror {
  statusCode: number
  data?: any
}

class AppError extends Error {
  constructor(message?: string | [string], statusCode: any = 400) {
    super(message instanceof Array ? message.join(', ') : message)

    this.name = this.constructor.name
    this.statusCode = statusCode
  }

  json(): ResponseObject {
    const response: ResponseObject = {
      name: this.name,
      statusCode: this.statusCode,
      message: this.message
    }

    if (this.data !== null && typeof this.data !== 'undefined') {
      response.data = this.data
    }

    return response
  }
}

export default AppError
